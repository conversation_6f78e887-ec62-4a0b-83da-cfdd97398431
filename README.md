# Prompt Manager Chrome 插件原型设计

这是一个基于 Tailwind CSS、shadcn/ui 和 Lucide 图标的 Prompt Manager Chrome 插件原型设计。

## 文件说明

### 1. `prototype.html`
基础原型页面，包含：
- 右侧固定面板设计
- 搜索功能界面
- Prompt 列表展示
- 预览区域
- 添加/编辑模态框
- 基础交互功能

### 2. `components.html`
组件库展示页面，包含：
- 按钮组件（主要、次要、幽灵按钮）
- 输入组件（搜索框、文本输入、文本域、选择框）
- 标签组件（不同颜色的文件夹标签）
- 提示框组件（信息、成功、警告、错误）
- 键盘快捷键显示
- Prompt 列表项样式

### 3. `demo.html` + `demo-script.js`
完整功能演示页面，包含：
- 完整的交互功能
- 键盘快捷键支持（Alt+S 唤出面板）
- 实时搜索功能
- 文件夹过滤
- # 快捷补全功能
- Prompt 管理（添加、编辑、删除）
- 数据导入导出
- 通知系统

## 主要功能特性

### 🔍 搜索功能
- 实时搜索，无需按回车
- 支持搜索标题、描述、内容和文件夹名称
- 搜索结果高亮显示

### ⌨️ 键盘快捷键
- `Alt + S`: 唤出/隐藏面板
- `↑/↓`: 在 Prompt 列表中导航
- `Enter`: 插入选中的 Prompt
- `#`: 在输入框中触发快捷补全

### 📁 文件夹管理
- 支持按文件夹过滤 Prompt
- 不同文件夹使用不同颜色标识
- 支持无文件夹的 Prompt

### ⚡ 快捷补全
- 在任意输入框中输入 `#` 触发补全
- 支持键盘导航选择
- 自动替换 `#关键词` 为完整 Prompt 内容

### 💾 数据管理
- 支持导出所有 Prompt 为 JSON 文件
- 支持从 JSON 文件导入 Prompt
- 本地存储（实际插件中会使用 Chrome Storage API）

## 设计特点

### 🎨 视觉设计
- 使用 Tailwind CSS 实现现代化 UI
- 遵循 shadcn/ui 设计规范
- 使用 Lucide 图标库提供一致的图标风格
- 支持深色/浅色主题（可扩展）

### 🔄 交互设计
- 流畅的动画效果
- 直观的键盘导航
- 实时反馈和状态提示
- 响应式设计支持

### 🏗️ 架构设计
- 模块化的组件设计
- 可扩展的数据结构
- 清晰的事件处理机制
- 易于维护的代码结构

## 使用方法

1. **查看基础原型**：打开 `prototype.html`
2. **查看组件库**：打开 `components.html`
3. **体验完整功能**：打开 `demo.html`

### 在 demo.html 中体验功能：

1. **唤出面板**：按 `Alt + S` 或点击右侧面板
2. **搜索 Prompt**：在搜索框中输入关键词
3. **选择 Prompt**：使用鼠标点击或键盘方向键
4. **插入 Prompt**：按 `Enter` 键或双击
5. **快捷补全**：在输入框中输入 `#邮件` 等关键词
6. **管理 Prompt**：点击"添加 Prompt"按钮或编辑现有 Prompt

## 技术栈

- **CSS 框架**: Tailwind CSS
- **设计系统**: shadcn/ui 风格
- **图标库**: Lucide Icons
- **JavaScript**: 原生 ES6+
- **构建工具**: 无需构建，直接在浏览器中运行

## 浏览器兼容性

- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

## 下一步开发计划

1. **Chrome 插件集成**
   - 转换为 Chrome Extension Manifest V3
   - 实现 Content Script 注入
   - 使用 Chrome Storage API

2. **功能增强**
   - 拖拽排序功能
   - 更多文件夹管理选项
   - Prompt 模板变量支持
   - 使用统计和智能推荐

3. **性能优化**
   - 虚拟滚动支持大量 Prompt
   - 搜索性能优化
   - 内存使用优化

4. **用户体验**
   - 更多键盘快捷键
   - 自定义主题支持
   - 多语言支持

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
