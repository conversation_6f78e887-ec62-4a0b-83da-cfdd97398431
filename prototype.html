<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prompt Manager - 原型设计</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        /* 自定义样式 */
        .highlight {
            background-color: #fef08a;
            font-weight: 600;
        }
        
        .prompt-item:hover {
            background-color: #f8fafc;
        }
        
        .prompt-item.selected {
            background-color: #e0f2fe;
            border-left: 4px solid #0ea5e9;
        }
        
        .panel-shadow {
            box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- 主面板 - 固定在右侧 -->
    <div id="promptPanel" class="fixed right-0 top-0 h-full w-96 bg-white panel-shadow border-l border-gray-200 flex flex-col z-50">
        <!-- 头部 -->
        <div class="flex items-center justify-between p-4 border-b border-gray-200">
            <h1 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <i data-lucide="zap" class="w-5 h-5 text-blue-500"></i>
                Prompt Manager
            </h1>
            <button class="p-1 hover:bg-gray-100 rounded" onclick="togglePanel()">
                <i data-lucide="x" class="w-4 h-4 text-gray-500"></i>
            </button>
        </div>

        <!-- 搜索框 -->
        <div class="p-4 border-b border-gray-100">
            <div class="relative">
                <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"></i>
                <input 
                    type="text" 
                    id="searchInput"
                    placeholder="搜索 Prompt..." 
                    class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                    oninput="searchPrompts(this.value)"
                >
            </div>
        </div>

        <!-- Prompt 列表 -->
        <div class="flex-1 overflow-y-auto">
            <div id="promptList" class="p-2">
                <!-- Prompt 项目 -->
                <div class="prompt-item selected p-3 rounded-lg cursor-pointer mb-2 border border-transparent hover:border-gray-200" onclick="selectPrompt(this)">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <h3 class="font-medium text-gray-900 mb-1">邮件回复模板</h3>
                            <p class="text-sm text-gray-600 mb-2">用于正式邮件回复的专业模板</p>
                            <span class="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">工作/邮件模板</span>
                        </div>
                        <div class="flex gap-1 ml-2">
                            <button class="p-1 hover:bg-gray-100 rounded" onclick="editPrompt(event)">
                                <i data-lucide="edit-3" class="w-3 h-3 text-gray-400"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="prompt-item p-3 rounded-lg cursor-pointer mb-2 border border-transparent hover:border-gray-200" onclick="selectPrompt(this)">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <h3 class="font-medium text-gray-900 mb-1">代码审查清单</h3>
                            <p class="text-sm text-gray-600 mb-2">用于代码审查的标准检查清单</p>
                            <span class="text-xs text-green-600 bg-green-50 px-2 py-1 rounded">开发/代码审查</span>
                        </div>
                        <div class="flex gap-1 ml-2">
                            <button class="p-1 hover:bg-gray-100 rounded" onclick="editPrompt(event)">
                                <i data-lucide="edit-3" class="w-3 h-3 text-gray-400"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="prompt-item p-3 rounded-lg cursor-pointer mb-2 border border-transparent hover:border-gray-200" onclick="selectPrompt(this)">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <h3 class="font-medium text-gray-900 mb-1">会议纪要模板</h3>
                            <p class="text-sm text-gray-600 mb-2">标准化的会议记录格式模板</p>
                            <span class="text-xs text-purple-600 bg-purple-50 px-2 py-1 rounded">工作/会议</span>
                        </div>
                        <div class="flex gap-1 ml-2">
                            <button class="p-1 hover:bg-gray-100 rounded" onclick="editPrompt(event)">
                                <i data-lucide="edit-3" class="w-3 h-3 text-gray-400"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 预览区域 -->
        <div id="previewArea" class="border-t border-gray-200 p-4 bg-gray-50 max-h-32 overflow-y-auto">
            <h4 class="text-sm font-medium text-gray-700 mb-2">内容预览</h4>
            <p class="text-sm text-gray-600 leading-relaxed">
                尊敬的 [收件人姓名]，<br><br>
                感谢您的邮件。关于您提到的 [具体事项]，我想回复如下：<br><br>
                [具体回复内容]<br><br>
                如有任何疑问，请随时联系我。<br><br>
                此致<br>
                敬礼<br><br>
                [您的姓名]
            </p>
        </div>

        <!-- 底部操作栏 -->
        <div class="border-t border-gray-200 p-4">
            <button class="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg flex items-center justify-center gap-2 transition-colors" onclick="showAddPromptModal()">
                <i data-lucide="plus" class="w-4 h-4"></i>
                添加 Prompt
            </button>
        </div>
    </div>

    <!-- 模拟的网页内容区域 -->
    <div class="pr-96 min-h-screen p-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-900 mb-8">Prompt Manager 原型演示</h1>
            
            <!-- 演示说明 -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                <h2 class="text-lg font-semibold text-blue-900 mb-3 flex items-center gap-2">
                    <i data-lucide="info" class="w-5 h-5"></i>
                    使用说明
                </h2>
                <ul class="text-blue-800 space-y-2">
                    <li>• 按 <kbd class="bg-white px-2 py-1 rounded border">Alt+S</kbd> 唤出/隐藏面板</li>
                    <li>• 在搜索框中输入关键词实时搜索</li>
                    <li>• 使用方向键选择 Prompt，按 <kbd class="bg-white px-2 py-1 rounded border">Enter</kbd> 插入</li>
                    <li>• 在输入框中输入 <kbd class="bg-white px-2 py-1 rounded border">#</kbd> 触发快捷补全</li>
                </ul>
            </div>

            <!-- 模拟输入框 -->
            <div class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">邮件编辑器</label>
                    <textarea 
                        class="w-full h-32 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none resize-none"
                        placeholder="在这里输入邮件内容，或输入 # 触发 Prompt 补全..."
                        oninput="handleHashInput(this)"
                    ></textarea>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">聊天输入框</label>
                    <div class="flex gap-2">
                        <input 
                            type="text" 
                            class="flex-1 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                            placeholder="输入消息，或使用 # 快捷插入 Prompt..."
                            oninput="handleHashInput(this)"
                        >
                        <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-lg">
                            <i data-lucide="send" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑 Prompt 模态框 -->
    <div id="promptModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">添加 Prompt</h2>
            </div>
            <div class="p-6 space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">标题 *</label>
                    <input type="text" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none" placeholder="输入 Prompt 标题">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">描述</label>
                    <input type="text" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none" placeholder="简短描述（可选）">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">文件夹</label>
                    <select class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none">
                        <option>无文件夹</option>
                        <option>工作</option>
                        <option>开发</option>
                        <option>个人</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">内容 *</label>
                    <textarea class="w-full h-40 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none resize-none" placeholder="输入 Prompt 内容"></textarea>
                </div>
            </div>
            <div class="p-6 border-t border-gray-200 flex justify-end gap-3">
                <button class="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors" onclick="hideAddPromptModal()">取消</button>
                <button class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors">保存</button>
            </div>
        </div>
    </div>

    <script>
        // 初始化 Lucide 图标
        lucide.createIcons();

        // 面板控制
        function togglePanel() {
            const panel = document.getElementById('promptPanel');
            panel.classList.toggle('hidden');
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.altKey && e.key === 's') {
                e.preventDefault();
                togglePanel();
                document.getElementById('searchInput').focus();
            }
        });

        // 搜索功能
        function searchPrompts(query) {
            // 这里实现搜索逻辑
            console.log('搜索:', query);
        }

        // 选择 Prompt
        function selectPrompt(element) {
            document.querySelectorAll('.prompt-item').forEach(item => {
                item.classList.remove('selected');
            });
            element.classList.add('selected');
        }

        // 编辑 Prompt
        function editPrompt(event) {
            event.stopPropagation();
            showAddPromptModal();
        }

        // 显示添加模态框
        function showAddPromptModal() {
            document.getElementById('promptModal').classList.remove('hidden');
            document.getElementById('promptModal').classList.add('flex');
        }

        // 隐藏添加模态框
        function hideAddPromptModal() {
            document.getElementById('promptModal').classList.add('hidden');
            document.getElementById('promptModal').classList.remove('flex');
        }

        // 处理 # 输入
        function handleHashInput(input) {
            const value = input.value;
            const cursorPos = input.selectionStart;
            
            // 检查是否输入了 #
            if (value.includes('#')) {
                console.log('检测到 # 输入，触发补全');
                // 这里实现补全逻辑
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认聚焦搜索框
            document.getElementById('searchInput').focus();
        });
    </script>
</body>
</html>
