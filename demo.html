<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prompt Manager - 完整演示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .highlight { background-color: #fef08a; font-weight: 600; }
        .prompt-item:hover { background-color: #f8fafc; }
        .prompt-item.selected { background-color: #e0f2fe; border-left: 4px solid #0ea5e9; }
        .panel-shadow { box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1); }
        .slide-in { animation: slideIn 0.3s ease-out; }
        .fade-in { animation: fadeIn 0.2s ease-out; }
        
        @keyframes slideIn {
            from { transform: translateX(100%); }
            to { transform: translateX(0); }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .autocomplete-dropdown {
            position: absolute;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }
        
        .autocomplete-item {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .autocomplete-item:hover,
        .autocomplete-item.selected {
            background-color: #f8fafc;
        }
        
        .autocomplete-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- 主面板 -->
    <div id="promptPanel" class="fixed right-0 top-0 h-full w-96 bg-white panel-shadow border-l border-gray-200 flex flex-col z-50 slide-in">
        <!-- 头部 -->
        <div class="flex items-center justify-between p-4 border-b border-gray-200">
            <h1 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <i data-lucide="zap" class="w-5 h-5 text-blue-500"></i>
                Prompt Manager
            </h1>
            <div class="flex items-center gap-2">
                <button class="p-1 hover:bg-gray-100 rounded" onclick="showSettings()">
                    <i data-lucide="settings" class="w-4 h-4 text-gray-500"></i>
                </button>
                <button class="p-1 hover:bg-gray-100 rounded" onclick="togglePanel()">
                    <i data-lucide="x" class="w-4 h-4 text-gray-500"></i>
                </button>
            </div>
        </div>

        <!-- 搜索框 -->
        <div class="p-4 border-b border-gray-100">
            <div class="relative">
                <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"></i>
                <input 
                    type="text" 
                    id="searchInput"
                    placeholder="搜索 Prompt..." 
                    class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                    oninput="searchPrompts(this.value)"
                    onkeydown="handleSearchKeydown(event)"
                >
            </div>
        </div>

        <!-- 文件夹过滤 -->
        <div class="px-4 pb-2">
            <div class="flex gap-2 overflow-x-auto">
                <button class="folder-filter active px-3 py-1 text-xs rounded-full bg-blue-100 text-blue-700 whitespace-nowrap" onclick="filterByFolder('all')">全部</button>
                <button class="folder-filter px-3 py-1 text-xs rounded-full bg-gray-100 text-gray-700 whitespace-nowrap" onclick="filterByFolder('work')">工作</button>
                <button class="folder-filter px-3 py-1 text-xs rounded-full bg-gray-100 text-gray-700 whitespace-nowrap" onclick="filterByFolder('dev')">开发</button>
                <button class="folder-filter px-3 py-1 text-xs rounded-full bg-gray-100 text-gray-700 whitespace-nowrap" onclick="filterByFolder('personal')">个人</button>
            </div>
        </div>

        <!-- Prompt 列表 -->
        <div class="flex-1 overflow-y-auto">
            <div id="promptList" class="p-2">
                <!-- 动态生成的 Prompt 项目 -->
            </div>
        </div>

        <!-- 预览区域 -->
        <div id="previewArea" class="border-t border-gray-200 p-4 bg-gray-50 max-h-32 overflow-y-auto">
            <h4 class="text-sm font-medium text-gray-700 mb-2">内容预览</h4>
            <p id="previewContent" class="text-sm text-gray-600 leading-relaxed">
                选择一个 Prompt 查看预览内容
            </p>
        </div>

        <!-- 底部操作栏 -->
        <div class="border-t border-gray-200 p-4 space-y-2">
            <button class="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg flex items-center justify-center gap-2 transition-colors" onclick="showAddPromptModal()">
                <i data-lucide="plus" class="w-4 h-4"></i>
                添加 Prompt
            </button>
            <div class="flex gap-2">
                <button class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-3 rounded-lg flex items-center justify-center gap-2 text-sm transition-colors" onclick="exportData()">
                    <i data-lucide="download" class="w-4 h-4"></i>
                    导出
                </button>
                <button class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-3 rounded-lg flex items-center justify-center gap-2 text-sm transition-colors" onclick="importData()">
                    <i data-lucide="upload" class="w-4 h-4"></i>
                    导入
                </button>
            </div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="pr-96 min-h-screen p-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-900 mb-8">Prompt Manager 完整演示</h1>
            
            <!-- 功能演示区域 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- 快捷键说明 -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <h2 class="text-lg font-semibold text-blue-900 mb-4 flex items-center gap-2">
                        <i data-lucide="keyboard" class="w-5 h-5"></i>
                        快捷键
                    </h2>
                    <div class="space-y-2 text-blue-800">
                        <div class="flex items-center gap-2">
                            <kbd class="bg-white px-2 py-1 rounded border text-xs">Alt+S</kbd>
                            <span class="text-sm">唤出/隐藏面板</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <kbd class="bg-white px-2 py-1 rounded border text-xs">↑↓</kbd>
                            <span class="text-sm">选择 Prompt</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <kbd class="bg-white px-2 py-1 rounded border text-xs">Enter</kbd>
                            <span class="text-sm">插入 Prompt</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <kbd class="bg-white px-2 py-1 rounded border text-xs">#</kbd>
                            <span class="text-sm">快捷补全</span>
                        </div>
                    </div>
                </div>

                <!-- 状态显示 -->
                <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                    <h2 class="text-lg font-semibold text-green-900 mb-4 flex items-center gap-2">
                        <i data-lucide="activity" class="w-5 h-5"></i>
                        状态信息
                    </h2>
                    <div class="space-y-2 text-green-800 text-sm">
                        <div>总 Prompt 数量: <span id="totalCount" class="font-semibold">12</span></div>
                        <div>当前选中: <span id="selectedPrompt" class="font-semibold">邮件回复模板</span></div>
                        <div>最后使用: <span id="lastUsed" class="font-semibold">2分钟前</span></div>
                        <div>面板状态: <span id="panelStatus" class="font-semibold">已打开</span></div>
                    </div>
                </div>
            </div>

            <!-- 测试输入区域 -->
            <div class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">邮件编辑器 (支持 # 快捷补全)</label>
                    <div class="relative">
                        <textarea 
                            id="emailEditor"
                            class="w-full h-32 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none resize-none"
                            placeholder="在这里输入邮件内容，或输入 # 触发 Prompt 补全..."
                            oninput="handleHashInput(this)"
                            onkeydown="handleTextareaKeydown(event)"
                        ></textarea>
                        <div id="emailAutocomplete" class="autocomplete-dropdown"></div>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">聊天输入框</label>
                    <div class="flex gap-2">
                        <div class="flex-1 relative">
                            <input 
                                id="chatInput"
                                type="text" 
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                                placeholder="输入消息，或使用 # 快捷插入 Prompt..."
                                oninput="handleHashInput(this)"
                                onkeydown="handleInputKeydown(event)"
                            >
                            <div id="chatAutocomplete" class="autocomplete-dropdown"></div>
                        </div>
                        <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-lg transition-colors" onclick="sendMessage()">
                            <i data-lucide="send" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">代码编辑器</label>
                    <div class="relative">
                        <textarea 
                            id="codeEditor"
                            class="w-full h-40 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none resize-none font-mono text-sm"
                            placeholder="// 在这里编写代码，使用 # 插入代码模板..."
                            oninput="handleHashInput(this)"
                            onkeydown="handleTextareaKeydown(event)"
                        ></textarea>
                        <div id="codeAutocomplete" class="autocomplete-dropdown"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑 Prompt 模态框 -->
    <div id="promptModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto fade-in">
            <div class="p-6 border-b border-gray-200">
                <h2 id="modalTitle" class="text-xl font-semibold text-gray-900">添加 Prompt</h2>
            </div>
            <div class="p-6 space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">标题 *</label>
                    <input type="text" id="promptTitle" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none" placeholder="输入 Prompt 标题">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">描述</label>
                    <input type="text" id="promptDescription" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none" placeholder="简短描述（可选）">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">文件夹</label>
                    <select id="promptFolder" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none">
                        <option value="">无文件夹</option>
                        <option value="work">工作</option>
                        <option value="dev">开发</option>
                        <option value="personal">个人</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">内容 *</label>
                    <textarea id="promptContent" class="w-full h-40 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none resize-none" placeholder="输入 Prompt 内容"></textarea>
                </div>
            </div>
            <div class="p-6 border-t border-gray-200 flex justify-end gap-3">
                <button class="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors" onclick="hideAddPromptModal()">取消</button>
                <button class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors" onclick="savePrompt()">保存</button>
            </div>
        </div>
    </div>

    <!-- 通知提示 -->
    <div id="notification" class="fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg hidden fade-in">
        <div class="flex items-center gap-2">
            <i data-lucide="check-circle" class="w-4 h-4"></i>
            <span id="notificationText">操作成功</span>
        </div>
    </div>

    <script src="demo-script.js"></script>
</body>
</html>
