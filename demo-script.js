// 模拟数据
let prompts = [
    {
        id: 1,
        title: "邮件回复模板",
        description: "用于正式邮件回复的专业模板",
        content: "尊敬的 [收件人姓名]，\n\n感谢您的邮件。关于您提到的 [具体事项]，我想回复如下：\n\n[具体回复内容]\n\n如有任何疑问，请随时联系我。\n\n此致\n敬礼\n\n[您的姓名]",
        folder: "work",
        folderName: "工作/邮件模板"
    },
    {
        id: 2,
        title: "代码审查清单",
        description: "用于代码审查的标准检查清单",
        content: "## 代码审查清单\n\n### 功能性\n- [ ] 代码是否实现了预期功能\n- [ ] 边界条件是否处理正确\n- [ ] 错误处理是否完善\n\n### 代码质量\n- [ ] 代码是否易读易懂\n- [ ] 变量命名是否规范\n- [ ] 函数是否单一职责\n\n### 性能\n- [ ] 是否存在性能瓶颈\n- [ ] 算法复杂度是否合理",
        folder: "dev",
        folderName: "开发/代码审查"
    },
    {
        id: 3,
        title: "会议纪要模板",
        description: "标准化的会议记录格式模板",
        content: "# 会议纪要\n\n**会议主题：** [会议主题]\n**时间：** [日期时间]\n**参会人员：** [参会人员列表]\n**主持人：** [主持人姓名]\n\n## 会议议程\n1. [议程项目1]\n2. [议程项目2]\n\n## 讨论要点\n- [要点1]\n- [要点2]\n\n## 决议事项\n- [决议1]\n- [决议2]\n\n## 行动项\n| 任务 | 负责人 | 截止时间 |\n|------|--------|----------|\n| [任务1] | [负责人] | [时间] |",
        folder: "work",
        folderName: "工作/会议"
    },
    {
        id: 4,
        title: "Bug 报告模板",
        description: "标准的 Bug 报告格式",
        content: "## Bug 报告\n\n**Bug 标题：** [简短描述]\n\n**环境信息：**\n- 操作系统：\n- 浏览器版本：\n- 应用版本：\n\n**重现步骤：**\n1. [步骤1]\n2. [步骤2]\n3. [步骤3]\n\n**预期结果：**\n[描述预期的正确行为]\n\n**实际结果：**\n[描述实际发生的错误行为]\n\n**截图/日志：**\n[附加相关截图或错误日志]",
        folder: "dev",
        folderName: "开发/Bug报告"
    },
    {
        id: 5,
        title: "项目提案模板",
        description: "项目提案的标准格式",
        content: "# 项目提案\n\n## 项目概述\n**项目名称：** [项目名称]\n**项目负责人：** [负责人姓名]\n**预计时间：** [开始时间] - [结束时间]\n\n## 项目背景\n[描述项目背景和必要性]\n\n## 项目目标\n- [目标1]\n- [目标2]\n- [目标3]\n\n## 项目范围\n### 包含内容\n- [内容1]\n- [内容2]\n\n### 不包含内容\n- [排除内容1]\n- [排除内容2]\n\n## 资源需求\n- 人力资源：[人员配置]\n- 技术资源：[技术要求]\n- 预算：[预算估算]",
        folder: "work",
        folderName: "工作/项目管理"
    }
];

let currentSelectedIndex = 0;
let filteredPrompts = [...prompts];
let currentFolder = 'all';
let autocompleteIndex = -1;
let currentAutocompleteElement = null;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
    renderPromptList();
    updatePreview();
    document.getElementById('searchInput').focus();
});

// 键盘快捷键
document.addEventListener('keydown', function(e) {
    if (e.altKey && e.key === 's') {
        e.preventDefault();
        togglePanel();
        setTimeout(() => {
            document.getElementById('searchInput').focus();
        }, 100);
    }
});

// 面板控制
function togglePanel() {
    const panel = document.getElementById('promptPanel');
    panel.classList.toggle('hidden');
    updatePanelStatus();
}

function updatePanelStatus() {
    const panel = document.getElementById('promptPanel');
    const status = panel.classList.contains('hidden') ? '已关闭' : '已打开';
    document.getElementById('panelStatus').textContent = status;
}

// 搜索功能
function searchPrompts(query) {
    if (!query.trim()) {
        filteredPrompts = prompts.filter(p => currentFolder === 'all' || p.folder === currentFolder);
    } else {
        const searchTerm = query.toLowerCase();
        filteredPrompts = prompts.filter(prompt => {
            if (currentFolder !== 'all' && prompt.folder !== currentFolder) return false;
            
            return prompt.title.toLowerCase().includes(searchTerm) ||
                   prompt.description.toLowerCase().includes(searchTerm) ||
                   prompt.content.toLowerCase().includes(searchTerm) ||
                   prompt.folderName.toLowerCase().includes(searchTerm);
        });
    }
    
    currentSelectedIndex = 0;
    renderPromptList(query);
    updatePreview();
}

// 处理搜索框键盘事件
function handleSearchKeydown(event) {
    if (event.key === 'ArrowDown') {
        event.preventDefault();
        navigatePrompts(1);
    } else if (event.key === 'ArrowUp') {
        event.preventDefault();
        navigatePrompts(-1);
    } else if (event.key === 'Enter') {
        event.preventDefault();
        insertPrompt();
    }
}

// 导航 Prompt 列表
function navigatePrompts(direction) {
    if (filteredPrompts.length === 0) return;
    
    currentSelectedIndex += direction;
    if (currentSelectedIndex < 0) currentSelectedIndex = filteredPrompts.length - 1;
    if (currentSelectedIndex >= filteredPrompts.length) currentSelectedIndex = 0;
    
    renderPromptList();
    updatePreview();
}

// 渲染 Prompt 列表
function renderPromptList(searchQuery = '') {
    const listContainer = document.getElementById('promptList');
    
    if (filteredPrompts.length === 0) {
        listContainer.innerHTML = `
            <div class="text-center py-8 text-gray-500">
                <i data-lucide="search-x" class="w-8 h-8 mx-auto mb-2"></i>
                <p>未找到匹配的 Prompt</p>
            </div>
        `;
        lucide.createIcons();
        return;
    }
    
    listContainer.innerHTML = filteredPrompts.map((prompt, index) => {
        const isSelected = index === currentSelectedIndex;
        const highlightedTitle = highlightText(prompt.title, searchQuery);
        const highlightedDescription = highlightText(prompt.description, searchQuery);
        const highlightedFolder = highlightText(prompt.folderName, searchQuery);
        
        return `
            <div class="prompt-item ${isSelected ? 'selected' : ''} p-3 rounded-lg cursor-pointer mb-2 border border-transparent hover:border-gray-200" 
                 onclick="selectPrompt(${index})">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <h3 class="font-medium text-gray-900 mb-1">${highlightedTitle}</h3>
                        <p class="text-sm text-gray-600 mb-2">${highlightedDescription}</p>
                        <span class="text-xs px-2 py-1 rounded ${getFolderColor(prompt.folder)}">${highlightedFolder}</span>
                    </div>
                    <div class="flex gap-1 ml-2">
                        <button class="p-1 hover:bg-gray-100 rounded" onclick="editPrompt(event, ${prompt.id})">
                            <i data-lucide="edit-3" class="w-3 h-3 text-gray-400"></i>
                        </button>
                        <button class="p-1 hover:bg-gray-100 rounded" onclick="deletePrompt(event, ${prompt.id})">
                            <i data-lucide="trash-2" class="w-3 h-3 text-gray-400"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }).join('');
    
    lucide.createIcons();
    updateStats();
}

// 高亮搜索文本
function highlightText(text, query) {
    if (!query || !query.trim()) return text;
    
    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    return text.replace(regex, '<span class="highlight">$1</span>');
}

// 获取文件夹颜色
function getFolderColor(folder) {
    const colors = {
        'work': 'text-blue-600 bg-blue-50',
        'dev': 'text-green-600 bg-green-50',
        'personal': 'text-purple-600 bg-purple-50'
    };
    return colors[folder] || 'text-gray-600 bg-gray-50';
}

// 选择 Prompt
function selectPrompt(index) {
    currentSelectedIndex = index;
    renderPromptList();
    updatePreview();
}

// 更新预览
function updatePreview() {
    const previewContent = document.getElementById('previewContent');
    if (filteredPrompts.length === 0 || currentSelectedIndex >= filteredPrompts.length) {
        previewContent.textContent = '选择一个 Prompt 查看预览内容';
        return;
    }
    
    const selectedPrompt = filteredPrompts[currentSelectedIndex];
    const preview = selectedPrompt.content.substring(0, 200) + (selectedPrompt.content.length > 200 ? '...' : '');
    previewContent.textContent = preview;
    
    document.getElementById('selectedPrompt').textContent = selectedPrompt.title;
}

// 插入 Prompt
function insertPrompt() {
    if (filteredPrompts.length === 0) return;
    
    const selectedPrompt = filteredPrompts[currentSelectedIndex];
    const activeElement = document.activeElement;
    
    // 如果当前焦点在搜索框，找到最后活跃的输入框
    let targetElement = activeElement;
    if (activeElement.id === 'searchInput') {
        targetElement = document.querySelector('textarea:focus, input[type="text"]:focus') || 
                      document.getElementById('emailEditor');
    }
    
    if (targetElement && (targetElement.tagName === 'TEXTAREA' || targetElement.tagName === 'INPUT')) {
        const currentValue = targetElement.value;
        const cursorPos = targetElement.selectionStart;
        
        const newValue = currentValue.slice(0, cursorPos) + selectedPrompt.content + currentValue.slice(cursorPos);
        targetElement.value = newValue;
        
        // 设置光标位置
        const newCursorPos = cursorPos + selectedPrompt.content.length;
        targetElement.setSelectionRange(newCursorPos, newCursorPos);
        
        // 聚焦到目标元素
        targetElement.focus();
        
        showNotification(`已插入 "${selectedPrompt.title}"`);
        updateLastUsed();
    }
}

// # 快捷补全功能
function handleHashInput(element) {
    const value = element.value;
    const cursorPos = element.selectionStart;
    
    // 查找最近的 # 位置
    let hashPos = -1;
    for (let i = cursorPos - 1; i >= 0; i--) {
        if (value[i] === '#') {
            hashPos = i;
            break;
        }
        if (value[i] === ' ' || value[i] === '\n') {
            break;
        }
    }
    
    if (hashPos !== -1) {
        const query = value.substring(hashPos + 1, cursorPos);
        showAutocomplete(element, query, hashPos);
    } else {
        hideAutocomplete(element);
    }
}

// 显示自动补全
function showAutocomplete(element, query, hashPos) {
    const autocompleteId = element.id + 'Autocomplete';
    const autocompleteElement = document.getElementById(autocompleteId);
    
    if (!autocompleteElement) return;
    
    const matchedPrompts = prompts.filter(prompt => 
        prompt.title.toLowerCase().includes(query.toLowerCase()) ||
        prompt.description.toLowerCase().includes(query.toLowerCase())
    ).slice(0, 5);
    
    if (matchedPrompts.length === 0) {
        hideAutocomplete(element);
        return;
    }
    
    autocompleteElement.innerHTML = matchedPrompts.map((prompt, index) => `
        <div class="autocomplete-item ${index === 0 ? 'selected' : ''}" data-index="${index}" data-prompt-id="${prompt.id}">
            <div class="font-medium text-sm">${prompt.title}</div>
            <div class="text-xs text-gray-500">${prompt.description}</div>
        </div>
    `).join('');
    
    autocompleteElement.style.display = 'block';
    autocompleteElement.style.top = '100%';
    autocompleteElement.style.left = '0';
    autocompleteElement.style.right = '0';
    
    currentAutocompleteElement = element;
    autocompleteIndex = 0;
    
    // 添加点击事件
    autocompleteElement.querySelectorAll('.autocomplete-item').forEach((item, index) => {
        item.addEventListener('click', () => {
            selectAutocompleteItem(element, index, hashPos);
        });
    });
}

// 隐藏自动补全
function hideAutocomplete(element) {
    const autocompleteId = element.id + 'Autocomplete';
    const autocompleteElement = document.getElementById(autocompleteId);
    if (autocompleteElement) {
        autocompleteElement.style.display = 'none';
    }
    currentAutocompleteElement = null;
    autocompleteIndex = -1;
}

// 处理输入框键盘事件
function handleInputKeydown(event) {
    if (currentAutocompleteElement && currentAutocompleteElement === event.target) {
        const autocompleteElement = document.getElementById(event.target.id + 'Autocomplete');
        const items = autocompleteElement.querySelectorAll('.autocomplete-item');
        
        if (event.key === 'ArrowDown') {
            event.preventDefault();
            autocompleteIndex = Math.min(autocompleteIndex + 1, items.length - 1);
            updateAutocompleteSelection(items);
        } else if (event.key === 'ArrowUp') {
            event.preventDefault();
            autocompleteIndex = Math.max(autocompleteIndex - 1, 0);
            updateAutocompleteSelection(items);
        } else if (event.key === 'Enter' && autocompleteIndex >= 0) {
            event.preventDefault();
            const hashPos = findHashPosition(event.target);
            selectAutocompleteItem(event.target, autocompleteIndex, hashPos);
        } else if (event.key === 'Escape') {
            hideAutocomplete(event.target);
        }
    }
}

// 处理文本域键盘事件
function handleTextareaKeydown(event) {
    handleInputKeydown(event);
}

// 更新自动补全选择
function updateAutocompleteSelection(items) {
    items.forEach((item, index) => {
        item.classList.toggle('selected', index === autocompleteIndex);
    });
}

// 选择自动补全项
function selectAutocompleteItem(element, index, hashPos) {
    const autocompleteElement = document.getElementById(element.id + 'Autocomplete');
    const items = autocompleteElement.querySelectorAll('.autocomplete-item');
    const selectedItem = items[index];
    const promptId = parseInt(selectedItem.dataset.promptId);
    const prompt = prompts.find(p => p.id === promptId);
    
    if (prompt) {
        const value = element.value;
        const cursorPos = element.selectionStart;
        
        // 替换从 # 到光标位置的内容
        const newValue = value.substring(0, hashPos) + prompt.content + value.substring(cursorPos);
        element.value = newValue;
        
        // 设置光标位置
        const newCursorPos = hashPos + prompt.content.length;
        element.setSelectionRange(newCursorPos, newCursorPos);
        
        showNotification(`已插入 "${prompt.title}"`);
        updateLastUsed();
    }
    
    hideAutocomplete(element);
}

// 查找 # 位置
function findHashPosition(element) {
    const value = element.value;
    const cursorPos = element.selectionStart;
    
    for (let i = cursorPos - 1; i >= 0; i--) {
        if (value[i] === '#') {
            return i;
        }
        if (value[i] === ' ' || value[i] === '\n') {
            break;
        }
    }
    return -1;
}

// 文件夹过滤
function filterByFolder(folder) {
    currentFolder = folder;
    
    // 更新按钮状态
    document.querySelectorAll('.folder-filter').forEach(btn => {
        btn.classList.remove('active', 'bg-blue-100', 'text-blue-700');
        btn.classList.add('bg-gray-100', 'text-gray-700');
    });
    
    event.target.classList.remove('bg-gray-100', 'text-gray-700');
    event.target.classList.add('active', 'bg-blue-100', 'text-blue-700');
    
    // 重新搜索
    const searchQuery = document.getElementById('searchInput').value;
    searchPrompts(searchQuery);
}

// 模态框控制
function showAddPromptModal() {
    document.getElementById('promptModal').classList.remove('hidden');
    document.getElementById('promptModal').classList.add('flex');
    document.getElementById('promptTitle').focus();
}

function hideAddPromptModal() {
    document.getElementById('promptModal').classList.add('hidden');
    document.getElementById('promptModal').classList.remove('flex');
    clearModalForm();
}

function clearModalForm() {
    document.getElementById('promptTitle').value = '';
    document.getElementById('promptDescription').value = '';
    document.getElementById('promptContent').value = '';
    document.getElementById('promptFolder').value = '';
}

// 保存 Prompt
function savePrompt() {
    const title = document.getElementById('promptTitle').value.trim();
    const description = document.getElementById('promptDescription').value.trim();
    const content = document.getElementById('promptContent').value.trim();
    const folder = document.getElementById('promptFolder').value;
    
    if (!title || !content) {
        showNotification('请填写标题和内容', 'error');
        return;
    }
    
    const newPrompt = {
        id: Date.now(),
        title,
        description: description || title.substring(0, 50) + '...',
        content,
        folder: folder || '',
        folderName: folder ? getFolderName(folder) : '无文件夹'
    };
    
    prompts.push(newPrompt);
    hideAddPromptModal();
    searchPrompts(document.getElementById('searchInput').value);
    showNotification('Prompt 保存成功');
}

function getFolderName(folder) {
    const folderNames = {
        'work': '工作',
        'dev': '开发',
        'personal': '个人'
    };
    return folderNames[folder] || '其他';
}

// 编辑 Prompt
function editPrompt(event, promptId) {
    event.stopPropagation();
    const prompt = prompts.find(p => p.id === promptId);
    if (prompt) {
        document.getElementById('modalTitle').textContent = '编辑 Prompt';
        document.getElementById('promptTitle').value = prompt.title;
        document.getElementById('promptDescription').value = prompt.description;
        document.getElementById('promptContent').value = prompt.content;
        document.getElementById('promptFolder').value = prompt.folder;
        showAddPromptModal();
    }
}

// 删除 Prompt
function deletePrompt(event, promptId) {
    event.stopPropagation();
    if (confirm('确定要删除这个 Prompt 吗？')) {
        prompts = prompts.filter(p => p.id !== promptId);
        searchPrompts(document.getElementById('searchInput').value);
        showNotification('Prompt 已删除');
    }
}

// 导出数据
function exportData() {
    const dataStr = JSON.stringify(prompts, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'prompts-backup.json';
    link.click();
    URL.revokeObjectURL(url);
    showNotification('数据导出成功');
}

// 导入数据
function importData() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const importedPrompts = JSON.parse(e.target.result);
                    prompts = [...prompts, ...importedPrompts];
                    searchPrompts(document.getElementById('searchInput').value);
                    showNotification('数据导入成功');
                } catch (error) {
                    showNotification('导入失败：文件格式错误', 'error');
                }
            };
            reader.readAsText(file);
        }
    };
    input.click();
}

// 显示通知
function showNotification(message, type = 'success') {
    const notification = document.getElementById('notification');
    const notificationText = document.getElementById('notificationText');
    
    notificationText.textContent = message;
    
    if (type === 'error') {
        notification.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg fade-in';
    } else {
        notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg fade-in';
    }
    
    notification.classList.remove('hidden');
    
    setTimeout(() => {
        notification.classList.add('hidden');
    }, 3000);
}

// 更新统计信息
function updateStats() {
    document.getElementById('totalCount').textContent = prompts.length;
}

// 更新最后使用时间
function updateLastUsed() {
    document.getElementById('lastUsed').textContent = '刚刚';
}

// 发送消息
function sendMessage() {
    const chatInput = document.getElementById('chatInput');
    if (chatInput.value.trim()) {
        showNotification('消息已发送');
        chatInput.value = '';
    }
}

// 设置
function showSettings() {
    showNotification('设置功能开发中...');
}
