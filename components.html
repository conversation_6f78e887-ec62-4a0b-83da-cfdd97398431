<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prompt Manager - 组件库</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        /* shadcn/ui 风格的组件样式 */
        .btn {
            @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
        }
        
        .btn-primary {
            @apply bg-blue-600 text-white hover:bg-blue-700;
        }
        
        .btn-secondary {
            @apply bg-gray-100 text-gray-900 hover:bg-gray-200;
        }
        
        .btn-ghost {
            @apply hover:bg-gray-100 hover:text-gray-900;
        }
        
        .btn-sm {
            @apply h-9 px-3;
        }
        
        .btn-md {
            @apply h-10 py-2 px-4;
        }
        
        .card {
            @apply rounded-lg border bg-white text-gray-950 shadow-sm;
        }
        
        .card-header {
            @apply flex flex-col space-y-1.5 p-6;
        }
        
        .card-content {
            @apply p-6 pt-0;
        }
        
        .input {
            @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
        }
        
        .textarea {
            @apply flex min-h-[80px] w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
        }
        
        .select {
            @apply flex h-10 w-full items-center justify-between rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
        }
        
        .badge {
            @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
        }
        
        .badge-default {
            @apply border-transparent bg-gray-900 text-gray-50 hover:bg-gray-900/80;
        }
        
        .badge-secondary {
            @apply border-transparent bg-gray-100 text-gray-900 hover:bg-gray-100/80;
        }
        
        .badge-outline {
            @apply text-gray-950;
        }
        
        .dropdown-content {
            @apply z-50 min-w-[8rem] overflow-hidden rounded-md border bg-white p-1 text-gray-950 shadow-md;
        }
        
        .dropdown-item {
            @apply relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-gray-100 focus:text-gray-900 data-[disabled]:pointer-events-none data-[disabled]:opacity-50;
        }
        
        .separator {
            @apply -mx-1 my-1 h-px bg-gray-200;
        }
        
        .tooltip {
            @apply z-50 overflow-hidden rounded-md border bg-gray-900 px-3 py-1.5 text-xs text-gray-50 animate-in fade-in-0 zoom-in-95;
        }
        
        .dialog-overlay {
            @apply fixed inset-0 z-50 bg-black/80;
        }
        
        .dialog-content {
            @apply fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-white p-6 shadow-lg duration-200 sm:rounded-lg;
        }
        
        .alert {
            @apply relative w-full rounded-lg border p-4;
        }
        
        .alert-info {
            @apply border-blue-200 bg-blue-50 text-blue-900;
        }
        
        .alert-success {
            @apply border-green-200 bg-green-50 text-green-900;
        }
        
        .alert-warning {
            @apply border-yellow-200 bg-yellow-50 text-yellow-900;
        }
        
        .alert-error {
            @apply border-red-200 bg-red-50 text-red-900;
        }
        
        .kbd {
            @apply inline-flex h-5 max-h-full items-center rounded border bg-gray-100 px-1 font-mono text-[10px] font-medium text-gray-500;
        }
        
        /* 自定义滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
        
        /* 动画 */
        @keyframes slideIn {
            from {
                transform: translateX(100%);
            }
            to {
                transform: translateX(0);
            }
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }
        
        .slide-in {
            animation: slideIn 0.3s ease-out;
        }
        
        .fade-in {
            animation: fadeIn 0.2s ease-out;
        }
        
        /* 高亮搜索结果 */
        .search-highlight {
            background-color: #fef08a;
            font-weight: 600;
            padding: 0 2px;
            border-radius: 2px;
        }
        
        /* 拖拽样式 */
        .dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }
        
        .drop-zone {
            border: 2px dashed #3b82f6;
            background-color: #eff6ff;
        }
        
        /* 快捷补全下拉框 */
        .autocomplete-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
        }
        
        .autocomplete-item {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .autocomplete-item:hover,
        .autocomplete-item.selected {
            background-color: #f8fafc;
        }
        
        .autocomplete-item:last-child {
            border-bottom: none;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .prompt-panel {
                width: 100vw;
                right: -100vw;
            }
            
            .prompt-panel.open {
                right: 0;
            }
        }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-6xl mx-auto space-y-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Prompt Manager - 组件展示</h1>
        
        <!-- 按钮组件 -->
        <div class="card">
            <div class="card-header">
                <h2 class="text-xl font-semibold">按钮组件</h2>
            </div>
            <div class="card-content">
                <div class="flex flex-wrap gap-4">
                    <button class="btn btn-primary btn-md">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        添加 Prompt
                    </button>
                    <button class="btn btn-secondary btn-md">
                        <i data-lucide="edit-3" class="w-4 h-4 mr-2"></i>
                        编辑
                    </button>
                    <button class="btn btn-ghost btn-md">
                        <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>
                        删除
                    </button>
                    <button class="btn btn-primary btn-sm">
                        <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                        导出
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 输入组件 -->
        <div class="card">
            <div class="card-header">
                <h2 class="text-xl font-semibold">输入组件</h2>
            </div>
            <div class="card-content space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">搜索框</label>
                    <div class="relative">
                        <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"></i>
                        <input type="text" class="input pl-10" placeholder="搜索 Prompt...">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">文本输入</label>
                    <input type="text" class="input" placeholder="输入标题">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">文本域</label>
                    <textarea class="textarea" placeholder="输入内容"></textarea>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">选择框</label>
                    <select class="select">
                        <option>选择文件夹</option>
                        <option>工作</option>
                        <option>开发</option>
                        <option>个人</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- 标签组件 -->
        <div class="card">
            <div class="card-header">
                <h2 class="text-xl font-semibold">标签组件</h2>
            </div>
            <div class="card-content">
                <div class="flex flex-wrap gap-2">
                    <span class="badge badge-default">工作</span>
                    <span class="badge badge-secondary">开发</span>
                    <span class="badge badge-outline border-blue-200 text-blue-700">邮件模板</span>
                    <span class="badge badge-outline border-green-200 text-green-700">代码审查</span>
                    <span class="badge badge-outline border-purple-200 text-purple-700">会议</span>
                </div>
            </div>
        </div>
        
        <!-- 提示框组件 -->
        <div class="card">
            <div class="card-header">
                <h2 class="text-xl font-semibold">提示框组件</h2>
            </div>
            <div class="card-content space-y-4">
                <div class="alert alert-info">
                    <i data-lucide="info" class="w-4 h-4 mr-2 inline"></i>
                    这是一个信息提示框
                </div>
                <div class="alert alert-success">
                    <i data-lucide="check-circle" class="w-4 h-4 mr-2 inline"></i>
                    Prompt 保存成功！
                </div>
                <div class="alert alert-warning">
                    <i data-lucide="alert-triangle" class="w-4 h-4 mr-2 inline"></i>
                    请填写必填字段
                </div>
                <div class="alert alert-error">
                    <i data-lucide="x-circle" class="w-4 h-4 mr-2 inline"></i>
                    删除操作失败
                </div>
            </div>
        </div>
        
        <!-- 键盘快捷键显示 -->
        <div class="card">
            <div class="card-header">
                <h2 class="text-xl font-semibold">快捷键显示</h2>
            </div>
            <div class="card-content">
                <div class="space-y-2">
                    <div class="flex items-center gap-2">
                        <span class="kbd">Alt</span>
                        <span>+</span>
                        <span class="kbd">S</span>
                        <span class="text-gray-600">唤出面板</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <span class="kbd">Enter</span>
                        <span class="text-gray-600">插入 Prompt</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <span class="kbd">↑</span>
                        <span class="kbd">↓</span>
                        <span class="text-gray-600">选择 Prompt</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <span class="kbd">#</span>
                        <span class="text-gray-600">快捷补全</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Prompt 列表项 -->
        <div class="card">
            <div class="card-header">
                <h2 class="text-xl font-semibold">Prompt 列表项</h2>
            </div>
            <div class="card-content space-y-3">
                <div class="p-3 rounded-lg border border-blue-200 bg-blue-50 cursor-pointer">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <h3 class="font-medium text-gray-900 mb-1">
                                <span class="search-highlight">邮件</span>回复模板
                            </h3>
                            <p class="text-sm text-gray-600 mb-2">用于正式<span class="search-highlight">邮件</span>回复的专业模板</p>
                            <span class="badge badge-outline border-blue-200 text-blue-700">工作/<span class="search-highlight">邮件</span>模板</span>
                        </div>
                        <div class="flex gap-1 ml-2">
                            <button class="btn btn-ghost p-1 h-auto">
                                <i data-lucide="edit-3" class="w-3 h-3"></i>
                            </button>
                            <button class="btn btn-ghost p-1 h-auto">
                                <i data-lucide="more-vertical" class="w-3 h-3"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="p-3 rounded-lg border border-gray-200 hover:border-gray-300 cursor-pointer">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <h3 class="font-medium text-gray-900 mb-1">代码审查清单</h3>
                            <p class="text-sm text-gray-600 mb-2">用于代码审查的标准检查清单</p>
                            <span class="badge badge-outline border-green-200 text-green-700">开发/代码审查</span>
                        </div>
                        <div class="flex gap-1 ml-2">
                            <button class="btn btn-ghost p-1 h-auto">
                                <i data-lucide="edit-3" class="w-3 h-3"></i>
                            </button>
                            <button class="btn btn-ghost p-1 h-auto">
                                <i data-lucide="more-vertical" class="w-3 h-3"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化 Lucide 图标
        lucide.createIcons();
    </script>
</body>
</html>
